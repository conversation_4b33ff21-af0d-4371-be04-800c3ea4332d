"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const db_1 = __importDefault(require("./config/db"));
const walletRoute_1 = __importDefault(require("./routes/walletRoute"));
const errorHandler_1 = require("./middlewares/errorHandler");
const app = (0, express_1.default)();
app.set("trust proxy", 1);
app.use((0, morgan_1.default)("dev"));
app.use((0, cors_1.default)());
app.use((0, helmet_1.default)());
app.use(express_1.default.json());
app.get("/", (req, res) => {
    res.send("Welcome to Coconut Wallet Service API");
});
// Wallet routes
app.use("/api/v1/wallets", walletRoute_1.default);
// Error handler
app.use(errorHandler_1.errorHandler);
(0, db_1.default)();
exports.default = app;
