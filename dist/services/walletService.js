"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletService = void 0;
const axios_1 = __importDefault(require("axios"));
const Wallet_1 = require("../models/Wallet");
const envValidator_1 = require("../utils/envValidator");
const Business_1 = require("../models/Business");
const mongoose_1 = __importDefault(require("mongoose"));
const Transaction_1 = require("../models/Transaction");
const otp_1 = require("../utils/otp");
class WalletService {
    constructor() {
        this.flutterwaveBaseUrl = envValidator_1.env.FLUTTERWAVE_BASE_URL;
        this.shippingWalletId = envValidator_1.env.SHIPPING_WALLET_ID;
        this.packagingWalletId = envValidator_1.env.PACKAGING_WALLET_ID;
        this.fillingWalletId = envValidator_1.env.FILLING_WALLET_ID;
        this.bankingWalletId = envValidator_1.env.BANKING_WALLET_ID;
        this.transferWalletId = envValidator_1.env.TRANSFER_WALLET_ID;
        this.flutterwaveSecret = envValidator_1.env.FLUTTERWAVE_SECRET_KEY;
        this.baniBaseUrl = envValidator_1.env.BANI_BASE_URL;
        this.moniSignature = envValidator_1.env.BANI_MONI_SIGNATURE;
        this.baniToken = envValidator_1.env.BANI_TOKEN;
    }
    //Flutterwave createWallet Method
    // async createWallet(
    //   businessId: string,
    //   email: string,
    //   phoneNumber: string,
    //   firstName: string,
    //   lastName: string,
    //   bvn: string,
    //   narration: string,
    //   primary?: boolean
    // ) {
    //   try {
    //     // **Validate businessId format**
    //     if (!mongoose.Types.ObjectId.isValid(businessId)) {
    //       throw new Error("Invalid businessId format");
    //     }
    //     // **Check if business exists**
    //     const businessExists = await Business.findById(businessId);
    //     if (!businessExists) {
    //       throw new Error("Business not found");
    //     }
    //     // Generate a unique transaction reference
    //     const txRef = `${firstName}_${Date.now()}`;
    //     // Flutterwave API request payload
    //     const requestData = {
    //       email, //CORRUPT THIS EMAIL
    //       tx_ref: txRef,
    //       phonenumber: phoneNumber,
    //       is_permanent: true,
    //       firstname: firstName,
    //       lastname: lastName,
    //       narration,
    //       bvn,
    //     };
    //     // Call Flutterwave API
    //     const response = await axios.post(
    //       `${this.flutterwaveBaseUrl}/virtual-account-numbers`,
    //       requestData,
    //       {
    //         headers: { Authorization: `Bearer ${this.flutterwaveSecret}` },
    //       }
    //     );
    //     const responseData = response.data;
    //     if (responseData.status !== "success") {
    //       throw new Error("Failed to create virtual account");
    //     }
    //     const {
    //       flw_ref,
    //       order_ref,
    //       account_number,
    //       bank_name,
    //       created_at,
    //     } = responseData.data;
    //     // Create and save the wallet in MongoDB
    //     const wallet = await Wallet.create({
    //       businessId,
    //       balance: 0,
    //       currency: "NGN", // Change if dynamic
    //       narration,
    //       txRef,
    //       flwRef: flw_ref,
    //       orderRef: order_ref,
    //       accountNumber: Number(account_number),
    //       accountStatus: "ACTIVE",
    //       bankName: bank_name,
    //       primary,
    //       firstName,
    //       lastName,
    //       email, //Use the Uncorrupted email here
    //       phoneNumber,
    //       createdAt: new Date(created_at),
    //     });
    //     return wallet;
    //   } catch (error: any) {
    //     throw new Error(
    //       `Error creating wallet: ${error?.response?.data?.message}`
    //     );
    //   }
    // }
    fetchAllWalletData() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const wallets = yield Wallet_1.Wallet.find();
                return wallets;
            }
            catch (error) {
                console.log(error);
                throw new Error("Error fetching wallets data");
            }
        });
    }
    createWallet(businessId, email, phoneNumber, firstName, lastName, bvn, narration, primary, businessName) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b;
            try {
                // **Validate businessId format**
                if (!mongoose_1.default.Types.ObjectId.isValid(businessId)) {
                    throw new Error("Invalid businessId format");
                }
                // // // **Check if business exists**
                const businessExists = yield Business_1.Business.findById(businessId);
                if (!businessExists) {
                    throw new Error("Business not found");
                }
                // Generate a unique transaction reference
                const txRef = `${firstName}_${Date.now()}`;
                // Flutterwave API request payload
                const requestData = {
                    email, //CORRUPT THIS EMAIL
                    tx_ref: txRef,
                    phonenumber: phoneNumber,
                    is_permanent: true,
                    firstname: firstName,
                    lastname: lastName,
                    narration,
                    bvn,
                };
                const customer = {
                    customer_first_name: firstName,
                    customer_last_name: lastName,
                    customer_phone: phoneNumber,
                    customer_email: email,
                    customer_address: "h",
                    customer_state: "h",
                    customer_city: "h"
                };
                console.log("creating new customer", customer);
                // Call Bani API
                const response = yield axios_1.default.post(`${this.baniBaseUrl}comhub/add_my_customer/`, customer, {
                    headers: {
                        Authorization: `Bearer ${this.baniToken}`,
                        "Content-Type": "application/json",
                        "moni-signature": this.moniSignature,
                    },
                });
                console.log("customer created");
                const responseData = response.data;
                if (!responseData.status) {
                    console.log("failed to create customer");
                    throw new Error("Failed to create virtual account");
                }
                const { customer_ref } = responseData;
                const walletPayload = {
                    pay_va_step: "direct",
                    country_code: "NG",
                    pay_currency: "NGN",
                    holder_account_type: "permanent",
                    customer_ref,
                    pay_ext_ref: txRef,
                    alternate_name: businessName ? businessName : firstName + " " + lastName,
                    holder_legal_number: bvn,
                    bank_name: "guaranty trust bank",
                };
                console.log("creating new bank");
                const walletResponse = yield axios_1.default.post(`${this.baniBaseUrl}partner/collection/bank_transfer/`, walletPayload, {
                    headers: {
                        Authorization: `Bearer ${this.baniToken}`,
                        "Content-Type": "application/json",
                        "moni-signature": this.moniSignature,
                    },
                });
                console.log("created new bank", walletResponse.data);
                const { payment_reference, holder_account_number, holder_bank_name, amount, payment_ext_reference, account_type, account_name } = walletResponse.data;
                //Business account implementation
                const oldWallet = yield Wallet_1.Wallet.findOne({ businessId });
                if (oldWallet) {
                    oldWallet.business_account_number = holder_account_number;
                    oldWallet.business_bank_name = holder_bank_name;
                    yield oldWallet.save();
                    return oldWallet;
                }
                // Create and save the wallet in MongoDB
                const wallet = yield Wallet_1.Wallet.create({
                    businessId,
                    balance: 0,
                    currency: "NGN", // Change if dynamic
                    narration,
                    txRef,
                    flwRef: txRef,
                    orderRef: txRef,
                    accountNumber: Number(holder_account_number),
                    accountStatus: "ACTIVE",
                    bankName: holder_bank_name,
                    primary,
                    firstName,
                    lastName,
                    email, //Use the Uncorrupted email here
                    phoneNumber,
                    createdAt: new Date(),
                });
                return wallet;
                // return walletResponse.data;
            }
            catch (error) {
                console.log("error somewhere");
                console.log((error));
                throw new Error(`Error creating wallet: ${(_b = (_a = error === null || error === void 0 ? void 0 : error.response) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.message}`);
            }
        });
    }
    /**
     * Get all bank details from Flutterwave
     */
    getAllBanks() {
        return __awaiter(this, arguments, void 0, function* (countryCode = "NG") {
            var _a, _b, _c;
            try {
                const response = yield axios_1.default.get(`${this.flutterwaveBaseUrl}/banks/${countryCode}`, {
                    headers: {
                        Authorization: `Bearer ${this.flutterwaveSecret}`,
                    },
                });
                if (response.data.status !== "success") {
                    throw new Error("Failed to fetch banks");
                }
                return response.data.data;
            }
            catch (error) {
                console.error("Error fetching banks:", ((_a = error.response) === null || _a === void 0 ? void 0 : _a.data) || error);
                throw new Error(`Failed to fetch banks: ${((_c = (_b = error === null || error === void 0 ? void 0 : error.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.message) || "Unknown error"}`);
            }
        });
    }
    getBusinessWallets(businessId) {
        return __awaiter(this, void 0, void 0, function* () {
            return Wallet_1.Wallet.find({ businessId });
        });
    }
    getTransactionsByWalletId(walletId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield Transaction_1.Transaction.find({ walletId }).sort({ createdAt: -1 });
        });
    }
    getWalletBalance(walletId) {
        return __awaiter(this, void 0, void 0, function* () {
            const wallet = yield Wallet_1.Wallet.findById(walletId);
            if (!wallet)
                throw new Error("Wallet not found");
            return wallet.balance;
        });
    }
    processPayment(walletId, amount, meta, service) {
        return __awaiter(this, void 0, void 0, function* () {
            const wallet = yield Wallet_1.Wallet.findById(walletId);
            console.log(walletId);
            if (!wallet)
                throw new Error("Wallet not found");
            const total = amount.reduce((prev, curr) => prev + curr, 0);
            if (wallet.balance < total)
                throw new Error("Insufficient funds");
            // Deduct balance       
            wallet.balance -= total;
            yield wallet.save();
            // Generate transactionId               
            const txId = (0, otp_1.generateTxId)();
            // User wallet transaction
            const transactions = [
                {
                    txId,
                    txRef: wallet.txRef,
                    walletId,
                    amount: total,
                    currency: wallet.currency,
                    type: "payment",
                    meta,
                    status: "successful",
                },
            ];
            let serviceWallets = [];
            switch (service) {
                case "shipping":
                    serviceWallets = [
                        { id: this.shippingWalletId, index: 0 },
                        { id: this.bankingWalletId, index: 1 },
                    ];
                    break;
                case "filling":
                    serviceWallets = [
                        { id: this.fillingWalletId, index: 0 },
                        { id: this.bankingWalletId, index: 1 },
                    ];
                    break;
                case "packaging":
                    serviceWallets = [
                        { id: this.packagingWalletId, index: 0 },
                        { id: this.bankingWalletId, index: 1 },
                    ];
                    break;
                case "transfer":
                    serviceWallets = [
                        { id: this.transferWalletId, index: 0 },
                        { id: this.bankingWalletId, index: 1 },
                    ];
            }
            // Fetch and update all service wallets in parallel
            const wallets = yield Promise.all(serviceWallets.map(({ id }) => Wallet_1.Wallet.findById(id)));
            wallets.forEach((serviceWallet, i) => {
                if (!serviceWallet)
                    throw new Error(`${service} wallet not found`);
                serviceWallet.balance += amount[serviceWallets[i].index];
            });
            // Save all updated wallets in parallel
            yield Promise.all(wallets.map((w) => w.save()));
            // Record App wallet transactions
            transactions.push(...serviceWallets.map(({ id, index }) => ({
                txId,
                txRef: wallet.txRef,
                walletId: id,
                amount: amount[index],
                currency: wallet.currency,
                type: "payment",
                meta,
                status: "successful",
            })));
            yield Transaction_1.Transaction.insertMany(transactions);
            return { message: "Transaction successful", txId };
        });
    }
    /**
     * Verify Transaction with Flutterwave
     */
    verifyTransaction(transactionId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const response = yield axios_1.default.get(`https://api.flutterwave.com/v3/transactions/${transactionId}/verify`, {
                    headers: {
                        Authorization: `Bearer ${this.flutterwaveSecret}`,
                    },
                });
                return response.data;
            }
            catch (error) {
                console.error("Error verifying transaction:", error);
                throw new Error("Failed to verify transaction with Flutterwave.");
            }
        });
    }
    /**
     * Transfer funds using Flutterwave's Create Transfer API
     */
    transferToWallet(recipientAccountNumber, amount, narration) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            try {
                const requestData = {
                    account_bank: "flutterwave_virtual", // Bank code for virtual accounts
                    account_number: recipientAccountNumber,
                    amount,
                    narration,
                    currency: "NGN",
                    reference: `TRF_${Date.now()}`,
                    debit_currency: "NGN", // Ensure debit currency is NGN
                };
                const response = yield axios_1.default.post(`${this.flutterwaveBaseUrl}/transfers`, // Use correct v3 API endpoint
                requestData, {
                    headers: {
                        Authorization: `Bearer ${this.flutterwaveSecret}`,
                        "Content-Type": "application/json",
                    },
                });
                return response.data;
            }
            catch (error) {
                console.error("Error transferring funds:", ((_a = error.response) === null || _a === void 0 ? void 0 : _a.data) || error);
                throw new Error(`Failed to transfer funds: ${((_c = (_b = error === null || error === void 0 ? void 0 : error.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.message) || "Unknown error"}`);
            }
        });
    }
    /**
     * Transfer funds to bank
     */
    transferToBank(accountBank, accountNumber, amount, narration) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            try {
                const requestData = {
                    account_bank: accountBank, // Bank code for virtual accounts
                    account_number: accountNumber,
                    amount,
                    narration,
                    currency: "NGN",
                    reference: `TRF_${Date.now()}`,
                    debit_currency: "NGN", // Ensure debit currency is NGN
                };
                const response = yield axios_1.default.post(`${this.flutterwaveBaseUrl}/transfers`, // Use correct v3 API endpoint
                requestData, {
                    headers: {
                        Authorization: `Bearer ${this.flutterwaveSecret}`,
                        "Content-Type": "application/json",
                    },
                });
                // await Transaction.create({
                //     txId:response.data.data.id,
                //     walletId: walletId,
                //     txRef: txRef,
                //     amount,
                //     currency:"NGN",
                //     type: "withdrawal",
                //     status:"successful",
                //     meta:response.data.data,
                //     paymentGatewayResponse:response,
                //   });
                return response.data;
            }
            catch (error) {
                console.error("Error transferring funds:", ((_a = error.response) === null || _a === void 0 ? void 0 : _a.data) || error);
                throw new Error(`Failed to transfer funds: ${((_c = (_b = error === null || error === void 0 ? void 0 : error.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.message) || "Unknown error"}`);
            }
        });
    }
    baniTransferToBank(receiverAmount, transferMethod, receiverCurrency, transferReceiverType, receiverAccountNumber, receiverCountryCode, receiverAccountName, receiverSortCode, senderAmount, senderCurrency, businessId, narration, meta, walletId) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const parsedReceiverAmount = parseFloat(receiverAmount);
            if (isNaN(parsedReceiverAmount) || parsedReceiverAmount <= 0) {
                throw new Error('Invalid receiver amount');
            }
            // const businessExists = await Wallet.findOne({businessId});
            // if (!businessExists) {
            //   throw new Error("Business not found");
            // }
            // if(businessExists.balance < parsedReceiverAmount) {
            //   throw new Error("Insufficient funds")  
            // }
            try {
                const requestData = {
                    payout_step: "direct",
                    receiver_currency: receiverCurrency,
                    receiver_amount: receiverAmount,
                    transfer_method: transferMethod,
                    transfer_receiver_type: transferReceiverType,
                    receiver_account_num: receiverAccountNumber,
                    receiver_country_code: receiverCountryCode,
                    receiver_account_name: receiverAccountName,
                    receiver_sort_code: receiverSortCode,
                    sender_amount: senderAmount,
                    sender_currency: senderCurrency,
                    transfer_ext_ref: `TRF_${Date.now()}`,
                    // source_partner_code: 'NGSTAN',
                    transfer_note: narration,
                    source_partner_code: 'NGSQGT'
                };
                console.log(JSON.stringify(requestData, null, 2));
                const response = yield axios_1.default.post(`${this.baniBaseUrl}partner/payout/initiate_transfer/`, // Use correct v3 API endpoint
                requestData, {
                    headers: {
                        Authorization: `Bearer ${this.baniToken}`,
                        "moni-signature": this.moniSignature,
                        "Content-Type": "application/json",
                    },
                });
                return response.data;
            }
            catch (error) {
                console.error("Error transferring funds:", ((_a = error.response) === null || _a === void 0 ? void 0 : _a.data) || error);
                console.log(JSON.stringify(error));
                throw new Error(`Failed to transfer funds: ${((_c = (_b = error === null || error === void 0 ? void 0 : error.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.message) || "Unknown error"}`);
            }
        });
    }
    /**
     * Update BVN for a Wallet via Flutterwave
     */
    updateBvn(walletId, newBvn) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b;
            try {
                const wallet = yield Wallet_1.Wallet.findById(walletId);
                if (!wallet)
                    throw new Error("Wallet not found");
                const requestData = {
                    order_ref: wallet.orderRef,
                    bvn: newBvn,
                };
                const response = yield axios_1.default.post(`${this.flutterwaveBaseUrl}/virtual-account-numbers/update-bvn`, requestData, {
                    headers: {
                        Authorization: `Bearer ${this.flutterwaveSecret}`,
                    },
                });
                const responseData = response.data;
                if (responseData.status !== "success") {
                    throw new Error("Failed to update BVN");
                }
                return responseData;
            }
            catch (error) {
                console.error("Error updating BVN:", error);
                throw new Error(`Failed to update BVN: ${(_b = (_a = error === null || error === void 0 ? void 0 : error.response) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.message}`);
            }
        });
    }
}
exports.WalletService = WalletService;
