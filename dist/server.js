"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const app_1 = __importDefault(require("./app"));
const envValidator_1 = require("./utils/envValidator");
const PORT = envValidator_1.env.WALLET_PORT || 5001; // Different port for wallet service
app_1.default.listen(PORT, () => {
    console.log(`Wallet Service running on port ${PORT}`);
    console.log(`Wallet API available at http://localhost:${PORT}/api/v1/wallets`);
});
