"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTransactionsByWalletId = exports.processPaymentSchema = exports.updateBvnSchema = exports.transferToBankSchema = exports.createWalletSchema = void 0;
const zod_1 = require("zod");
exports.createWalletSchema = zod_1.z.object({
    businessId: zod_1.z.string().min(1, "Business ID is required"),
    email: zod_1.z.string().min(1, "Email is required"),
    phoneNumber: zod_1.z.string().min(1, "Phone is required"),
    firstName: zod_1.z.string().min(1, "First Name is required"),
    lastName: zod_1.z.string().min(1, "Last Name is required"),
    narration: zod_1.z.string().min(1, "Narration is required"),
    bvn: zod_1.z.string().min(1, "BVN is required"),
    currency: zod_1.z.string().min(1, "Currency is required"),
});
exports.transferToBankSchema = zod_1.z.object({
    recipientAccountBank: zod_1.z.string().min(1, "Recipient Account Bank is required"),
    recipientAccountNumber: zod_1.z
        .string()
        .min(1, "Recipient Account Number is required"),
    amount: zod_1.z.number().positive("Amount must be greater than zero"),
    narration: zod_1.z.string().optional(),
});
exports.updateBvnSchema = zod_1.z.object({
    walletId: zod_1.z.string().min(24, "Invalid wallet ID"),
    bvn: zod_1.z.string().length(11, "BVN must be 11 digits"),
});
exports.processPaymentSchema = zod_1.z.object({
    walletId: zod_1.z.string().min(1, "Wallet ID is required"),
    amount: zod_1.z.array(zod_1.z.number().nonnegative("Amount must be a positive number")),
    meta: zod_1.z.record(zod_1.z.any()),
    service: zod_1.z.string().min(1, "Shipment ID is required"),
});
exports.getTransactionsByWalletId = zod_1.z.object({
    walletId: zod_1.z.string().min(1, "Wallet ID is required"),
});
