"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.env = void 0;
const zod_1 = require("zod");
const envSchema = zod_1.z.object({
    WALLET_PORT: zod_1.z.string().default("5001"),
    MONGO_URI: zod_1.z.string().min(1, "MONGO_URI is required"),
    JWT_SECRET: zod_1.z.string().min(1, "JWT_SECRET is required"),
    ADMIN_JWT_SECRET: zod_1.z.string().min(1, "ADMIN_JWT_SECRET is required"),
    // Flutterwave
    FLUTTERWAVE_BASE_URL: zod_1.z.string().min(1, "FLUTTERWAVE_BASE_URL is required"),
    FLUTTERWAVE_SECRET_KEY: zod_1.z
        .string()
        .min(1, "FLUTTERWAVE_SECRET_KEY is required"),
    // Wallets ids
    BANKING_WALLET_ID: zod_1.z.string().min(1, "BANKING_WALLET_ID is required"),
    SHIPPING_WALLET_ID: zod_1.z.string().min(1, "SHIPPING_WALLET_ID is required"),
    FILLING_WALLET_ID: zod_1.z.string().min(1, "FILLING_WALLET_ID is required"),
    PACKAGING_WALLET_ID: zod_1.z.string().min(1, "PACKAGING_WALLET_ID is required"),
    TRANSFER_WALLET_ID: zod_1.z.string().min(1, "TRANSFER_WALLET_ID is required"),
    //Bani
    BANI_BASE_URL: zod_1.z.string().min(1, "BANI_BASE_URL is required"),
    BANI_TOKEN: zod_1.z.string().min(1, "BANI_TOKEN is required"),
    BANI_MONI_SIGNATURE: zod_1.z.string().min(1, "BANI_MONI_SIGNATURE is required"),
    BANI_SHARED_KEY: zod_1.z.string().min(1, "BANI_MONI_SIGNATURE is required"),
});
exports.env = envSchema.parse(process.env);
