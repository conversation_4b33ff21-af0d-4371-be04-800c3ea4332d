"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateAdminCode = exports.otpExpiryTime = exports.generateTxId = exports.generateOTP = void 0;
/**
 * Generates a 6-digit OTP
 */
const generateOTP = () => {
    return Math.floor(100000 + Math.random() * 900000).toString();
};
exports.generateOTP = generateOTP;
/**
 * Generates a 16-digit random numbers
 */
const generateTxId = () => {
    return Math.floor(1000000000000000 + Math.random() * 9000000000000000).toString();
};
exports.generateTxId = generateTxId;
const otpExpiryTime = () => {
    return new Date(Date.now() + 10 * 60 * 1000); // 10 minutes expiry
};
exports.otpExpiryTime = otpExpiryTime;
// generate code for admin
const generateAdminCode = (length = 16) => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
    let code = '';
    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        code += characters[randomIndex];
    }
    return code;
};
exports.generateAdminCode = generateAdminCode;
