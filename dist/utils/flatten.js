"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.flattenObject = flattenObject;
function flattenObject(obj, prefix = '') {
    let result = {};
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const newKey = prefix ? `${prefix}.${key}` : key;
            if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
                Object.assign(result, flattenObject(obj[key], newKey));
            }
            else {
                result[newKey] = obj[key];
            }
        }
    }
    return result;
}
