"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Transaction = void 0;
const mongoose_1 = require("mongoose");
const TransactionSchema = new mongoose_1.Schema({
    walletId: { type: mongoose_1.Schema.Types.ObjectId, ref: "Wallet", required: true }, // Reference to Wallet collection
    txId: { type: String, required: true, unique: true },
    txRef: { type: String, required: true, unique: true },
    shipmentId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: "Shipment",
        required: false,
    }, // Optional reference to Shipment collection
    amount: { type: Number, required: true },
    currency: { type: String, required: true },
    type: {
        type: String,
        enum: ["funding", "payment", "withdrawal"],
        required: true,
    },
    status: {
        type: String,
        enum: ["pending", "successful", "failed"],
        default: "pending",
    },
    meta: {
        type: Map, // Allows any key-value pair structure
        of: mongoose_1.Schema.Types.Mixed, // Supports any value type
        required: true,
    },
    paymentGatewayResponse: { type: mongoose_1.Schema.Types.Mixed, required: false }, // Store Seerbit response
}, { timestamps: true });
exports.Transaction = (0, mongoose_1.model)("Transaction", TransactionSchema);
