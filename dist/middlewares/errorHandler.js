"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = void 0;
const zod_1 = require("zod");
const jsonwebtoken_1 = require("jsonwebtoken");
const mongoose_1 = __importDefault(require("mongoose"));
const axios_1 = __importDefault(require("axios"));
const errorHandler = (err, req, res, next) => {
    var _a, _b, _c, _d;
    console.error("🔥 Error:", err.message);
    let status = 500;
    let message = "Internal Server Error";
    let errors = undefined;
    // 🔹 Handle Zod Validation Errors (for request body validation)
    if (err instanceof zod_1.ZodError) {
        status = 400;
        message = "Validation Error";
        errors = err.flatten().fieldErrors;
    }
    // 🔹 Handle Mongoose Validation Errors
    else if (err instanceof mongoose_1.default.Error.ValidationError) {
        status = 400;
        message = "Mongoose Validation Error";
        errors = Object.values(err.errors).map((e) => e.message);
    }
    // 🔹 Handle Duplicate Key Errors (MongoDB unique constraint)
    else if (err.code === 11000) {
        status = 400;
        const duplicatedField = Object.keys(err.keyValue)[0]; // Get the duplicated field
        if (duplicatedField === "email") {
            message = "Email already exists. Please use a different email.";
        }
        else if (duplicatedField === "phone") {
            message =
                "Phone number already exists. Please use a different phone number.";
        }
        else if (duplicatedField === "businessName") {
            message =
                "Business name already exists. Please use a different business name.";
        }
        else {
            message = `${duplicatedField} already exists. Please use a different ${duplicatedField}.`;
        }
    }
    // 🔹 Handle Invalid ObjectId Errors (Mongoose CastError)
    else if (err.name === "CastError") {
        status = 400;
        message = `Invalid ${err.path}: ${err.value}`;
    }
    // 🔹 Handle JWT Errors (Invalid or Expired Tokens)
    else if (err instanceof jsonwebtoken_1.JsonWebTokenError) {
        status = 401;
        message = "Invalid or expired token";
    }
    // 🔹 Handle Axios Errors from ShippingService
    else if (axios_1.default.isAxiosError(err)) {
        status = ((_a = err.response) === null || _a === void 0 ? void 0 : _a.status) || 500;
        message = ((_c = (_b = err.response) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.message) || "Shipping service error";
        errors = ((_d = err.response) === null || _d === void 0 ? void 0 : _d.data) || undefined;
    }
    // 🔹 Handle Custom App Errors (e.g., manually thrown errors)
    else if (err.status) {
        status = err.status;
        message = err.message || "Error";
    }
    // 🔹 Handle any other errors that don't fall under the above cases
    else if (err.message) {
        message = err.message;
    }
    // Send the error response with the appropriate status, message, and success: false
    res.status(status).json({
        success: false,
        status,
        message,
        errors,
    });
};
exports.errorHandler = errorHandler;
