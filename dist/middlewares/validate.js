"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validate = void 0;
const formatValidationErrors = (errors) => {
    return Object.keys(errors).reduce((acc, key) => {
        var _a;
        if ((_a = errors[key]) === null || _a === void 0 ? void 0 : _a._errors) {
            acc[key] = errors[key]._errors;
        }
        return acc;
    }, {});
};
const validate = (schemas // Allow separate schemas for body and params
) => {
    return (req, res, next) => {
        const errors = {};
        // Validate body if schema exists
        if (schemas.body) {
            const bodyResult = schemas.body.safeParse(req.body);
            if (!bodyResult.success) {
                errors.body = formatValidationErrors(bodyResult.error.format());
            }
        }
        // Validate params if schema exists
        if (schemas.params) {
            const paramsResult = schemas.params.safeParse(req.params);
            if (!paramsResult.success) {
                errors.params = formatValidationErrors(paramsResult.error.format());
            }
        }
        // If there are errors, return response
        if (Object.keys(errors).length > 0) {
            return res.status(400).json({
                success: false,
                message: "Validation Error",
                errors,
            });
        }
        next();
    };
};
exports.validate = validate;
