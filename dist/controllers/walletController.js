"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletController = void 0;
const walletService_1 = require("../services/walletService");
const Wallet_1 = require("../models/Wallet");
const Transaction_1 = require("../models/Transaction");
const User_1 = require("../models/User");
class WalletController {
    constructor() {
        this.walletService = new walletService_1.WalletService();
        // Bind all methods to ensure `this` is correctly referenced
        this.createWallet = this.createWallet.bind(this);
        this.getAllBanks = this.getAllBanks.bind(this);
        this.getTransactionsByWalletId = this.getTransactionsByWalletId.bind(this);
        this.getBusinessWallets = this.getBusinessWallets.bind(this);
        this.getWalletBalance = this.getWalletBalance.bind(this);
        this.processPayment = this.processPayment.bind(this);
        this.transferToBank = this.transferToBank.bind(this);
        this.baniTransferToBank = this.baniTransferToBank.bind(this);
        this.updateBvn = this.updateBvn.bind(this);
    }
    createWallet(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { businessId, email, phoneNumber, firstName, lastName, bvn, narration, } = req.body;
                if (!businessId ||
                    !email ||
                    !phoneNumber ||
                    !firstName ||
                    !lastName ||
                    !bvn ||
                    !narration) {
                    return res.status(400).json({ message: "All fields are required" });
                }
                const wallet = yield this.walletService.createWallet(businessId, email, phoneNumber, firstName, lastName, bvn, narration);
                return res
                    .status(201)
                    .json({ message: "Wallet created successfully", data: wallet });
            }
            catch (error) {
                return res.status(500).json({ message: error.message });
            }
        });
    }
    /**
     * Get all banks
     */
    getAllBanks(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { countryCode } = req.query;
                const banks = yield this.walletService.getAllBanks(countryCode);
                return res.status(200).json(banks);
            }
            catch (error) {
                next(error);
            }
        });
    }
    getBusinessWallets(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { businessId } = req.params;
                const wallets = yield this.walletService.getBusinessWallets(businessId);
                res.status(200).json({ success: true, wallets });
            }
            catch (error) {
                next(error);
            }
        });
    }
    getWalletBalance(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { walletId } = req.params;
                const balance = yield this.walletService.getWalletBalance(walletId);
                res.status(200).json({ success: true, balance });
            }
            catch (error) {
                next(error);
            }
        });
    }
    /**
     * Get all transaction for a wallet
     */
    getTransactionsByWalletId(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { walletId } = req.params;
                const transactions = yield this.walletService.getTransactionsByWalletId(walletId);
                res.status(200).json({
                    message: "Transactions fetched successfully",
                    data: transactions,
                });
            }
            catch (error) {
                res.status(500).json({
                    message: "Failed to fetch transactions",
                    error: error.message,
                });
            }
        });
    }
    /**
     * Verify Flutterwave Transaction
     */
    verifyTransaction(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { transactionId } = req.params;
                if (!transactionId) {
                    return res.status(400).json({ error: "Transaction ID is required" });
                }
                const transactionDetails = yield this.walletService.verifyTransaction(transactionId);
                if (!transactionDetails) {
                    return res.status(404).json({ error: "Transaction not found" });
                }
                res.status(200).json(transactionDetails);
            }
            catch (error) {
                console.error("Transaction Verification Error:", error.message);
                res.status(500).json({ error: "Failed to verify transaction" });
            }
        });
    }
    processPayment(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const { walletId, amount, meta, service } = req.body;
                const response = yield this.walletService.processPayment(walletId, amount, meta, service);
                res.status(200).json({ success: true, response });
            }
            catch (error) {
                console.log(error === null || error === void 0 ? void 0 : error.message);
                console.log((_a = error === null || error === void 0 ? void 0 : error.response) === null || _a === void 0 ? void 0 : _a.data);
                next(error);
            }
        });
    }
    /**
     * Transfer funds to bank
     */
    transferToBank(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { recipientAccountBank, recipientAccountNumber, amount, narration, } = req.body;
                if (!recipientAccountBank ||
                    !recipientAccountNumber ||
                    !amount ||
                    !narration) {
                    return res.status(400).json({ error: "All fields are required" });
                }
                const transferResponse = yield this.walletService.transferToBank(recipientAccountBank, recipientAccountNumber, amount, narration);
                res.status(200).json({ success: true, data: transferResponse });
            }
            catch (error) {
                next(error);
            }
        });
    }
    baniTransferToBank(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { receiverAmount, transferMethod, receiverCurrency, transferReceiverType, receiverAccountNumber, receiverCountryCode, receiverAccountName, receiverSortCode, senderAmount, narration, senderCurrency, businessId, walletId, meta, } = req.body;
                if (senderAmount && senderAmount < 1000) {
                    return res
                        .status(400)
                        .json({ error: "Sender amount must be at least 1,000 naira" });
                }
                if (senderAmount && senderAmount !== receiverAmount) {
                    return res
                        .status(400)
                        .json({ error: "Sender amount must match receiver amount" });
                }
                if (!req.user) {
                    return res.status(401).json({ error: "Unauthorized" });
                }
                const user = yield User_1.User.findById(req.user.id);
                if (!user) {
                    return res.status(401).json({ error: "User not found" });
                }
                const getTransferCost = (plan) => {
                    switch (plan) {
                        case "free":
                            return 50;
                        case "sme":
                            return 30;
                        case "growth":
                            return 10;
                    }
                };
                yield this.walletService.processPayment(walletId, [parseInt(senderAmount), getTransferCost((user === null || user === void 0 ? void 0 : user.plan) || "free")], meta, "transfer");
                const transferResponse = yield this.walletService.baniTransferToBank(receiverAmount, transferMethod, receiverCurrency, transferReceiverType, receiverAccountNumber, receiverCountryCode, receiverAccountName, receiverSortCode, senderAmount, senderCurrency, businessId, narration, meta, walletId);
                res.status(200).json({ success: true, data: transferResponse });
            }
            catch (error) {
                next(error);
            }
        });
    }
    fetchWalletsData(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const page = parseInt(req.query.page) || 1; // Default to page 1
                const limit = parseInt(req.query.limit) || 10; // Default to 10 items per page
                const skip = (page - 1) * limit;
                const wallets = yield Wallet_1.Wallet.find().skip(skip).limit(limit);
                if (!wallets || wallets.length === 0) {
                    res.status(200).json({
                        success: true,
                        wallets: [],
                        pagination: {
                            currentPage: page,
                            totalPages: 0,
                            limit: 0,
                            totalItems: 0,
                        },
                    });
                }
                res.status(200).json({
                    success: true,
                    wallets,
                    pagination: {
                        currentPage: page,
                        totalPages: Math.ceil((yield Wallet_1.Wallet.countDocuments()) / limit),
                        limit,
                        totalItems: yield Wallet_1.Wallet.countDocuments(),
                    },
                });
            }
            catch (error) {
                console.log(error);
                next(error);
            }
        });
    }
    fetchAllTransactions(req, res, next) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const page = parseInt(req.query.page) || 1;
                const limit = parseInt(req.query.limit) || 10;
                const skip = (page - 1) * limit;
                const transactions = yield Transaction_1.Transaction.find().skip(skip).limit(limit);
                if (!transactions || transactions.length === 0) {
                    return {
                        success: true,
                        transactions: [],
                        message: "No transactions found",
                    };
                }
                res.status(200).json({
                    success: true,
                    transactions,
                    pagination: {
                        currentPage: page,
                        totalPages: Math.ceil((yield Transaction_1.Transaction.countDocuments()) / limit),
                        limit,
                        totalItems: yield Transaction_1.Transaction.countDocuments(),
                    },
                });
            }
            catch (error) {
                next(error);
            }
        });
    }
    /**
     * Update BVN for a wallet
     */
    updateBvn(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const { walletId, bvn } = req.body;
                if (!walletId || !bvn) {
                    return res
                        .status(400)
                        .json({ message: "walletId and bvn are required" });
                }
                const updatedWallet = yield this.walletService.updateBvn(walletId, bvn);
                return res
                    .status(200)
                    .json({ message: "BVN updated successfully", data: updatedWallet });
            }
            catch (error) {
                return res.status(500).json({ message: error.message });
            }
        });
    }
}
exports.WalletController = WalletController;
