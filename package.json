{"name": "coconut-wallet-service", "version": "1.0.0", "description": "Independent wallet service for Coconut API", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "rimraf dist && tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.6.3", "morgan": "^1.10.0", "zod": "^3.21.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.3", "@types/cors": "^2.8.14", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.2", "@types/mongoose": "^5.11.97", "@types/morgan": "^1.9.9", "@types/node": "^20.11.21", "nodemon": "^3.1.0", "rimraf": "^5.0.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.4.2"}}